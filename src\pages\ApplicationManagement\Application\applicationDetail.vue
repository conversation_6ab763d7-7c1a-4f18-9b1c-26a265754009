<template>
  <div class="application-detail-page">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>

    <!-- 基本信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="应用名称">{{ detailData.applicationName }}</el-descriptions-item>
            <el-descriptions-item label="所属产品">{{ detailData.product }}</el-descriptions-item>
            <el-descriptions-item label="所属机构">{{ detailData.organization }}</el-descriptions-item>
            <el-descriptions-item label="负责项目经理">{{ detailData.projectManager || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="SSO系统标识">{{ detailData.ssoSystemId || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="git地址及分支">{{ detailData.gitUrl || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="开发语言">{{ detailData.developmentLanguage || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="平均发布时长">{{ detailData.avgPublishDuration }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="8">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(detailData.status)">
                {{ getStatusText(detailData.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="负责开发组">{{ detailData.developmentTeam || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="应用大类">{{ detailData.applicationCategory || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="源码工程标识">{{ detailData.sourceCodeId || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="数据库标识">{{ detailData.databaseId || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="总发布次数">{{ detailData.publishCount }}</el-descriptions-item>
            <el-descriptions-item label="平均启动时长">{{ detailData.avgStartupDuration }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="8">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
            <el-descriptions-item label="类型">{{ detailData.type }}</el-descriptions-item>
            <el-descriptions-item label="负责测试组">{{ detailData.testingTeam || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="应用小类">{{ detailData.applicationSubCategory || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="生产应用标识">{{ detailData.productionAppId || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="OSS私有文件桶标识">{{ detailData.ossBucketId || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="总发布时长">{{ detailData.publishDuration }}</el-descriptions-item>
            <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <!-- 更多信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>更多信息</span>
        </div>
      </template>
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="依赖" name="dependencies">
          <el-table :data="dependenciesData" style="width: 100%" max-height="400">
            <el-table-column prop="groupId" label="groupId" width="200" />
            <el-table-column prop="artifactId" label="artifactId" width="200" />
            <el-table-column prop="version" label="version" width="150" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="应用日志" name="appLogs">
          <el-table :data="appLogsData" style="width: 100%" max-height="400">
            <el-table-column prop="serverIp" label="服务器IP" width="120" />
            <el-table-column prop="logPath" label="日志地址" width="200" />
            <el-table-column prop="fileCount" label="日志文件数量" width="120" />
            <el-table-column prop="over2GCount" label="超过2G的文件数量" width="150" />
            <el-table-column prop="maxStorage" label="最大文件存储量" width="120" />
            <el-table-column prop="createTime" label="创建时间" width="150" />
            <el-table-column prop="updateTime" label="更新时间" width="150" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="日志清理记录" name="logCleanup">
          <div class="log-cleanup-header">
            <el-button type="primary" size="small" @click="handleAddLogCleanup">
              添加
            </el-button>
          </div>
          <el-table :data="logCleanupData" style="width: 100%" max-height="400">
            <el-table-column prop="serverIp" label="服务器IP" width="120" />
            <el-table-column prop="filePath" label="清理文件路径" width="150" />
            <el-table-column prop="fileName" label="清理文件名称" width="120" />
            <el-table-column prop="cleanupTime" label="清理时间" width="150" />
            <el-table-column prop="operationType" label="操作类型" width="100" />
            <el-table-column prop="cleanupReason" label="清理原因" width="120" />
            <el-table-column prop="operationResult" label="操作结果" width="80" />
            <el-table-column prop="failureReason" label="失败原因" width="100" />
            <el-table-column prop="beforeSize" label="清理前大小" width="100" />
            <el-table-column prop="afterSize" label="清理后大小" width="100" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditLogCleanup(row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="handleDeleteLogCleanup(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 日志清理记录编辑对话框 -->
    <el-dialog
        v-model="logCleanupDialogVisible"
        :title="logCleanupDialogTitle"
        width="600px"
        @close="handleLogCleanupDialogClose"
    >
      <el-form
          ref="logCleanupFormRef"
          :model="logCleanupForm"
          :rules="logCleanupRules"
          label-width="120px"
      >
        <el-form-item label="服务器IP" prop="serverIp">
          <el-input v-model="logCleanupForm.serverIp" placeholder="请输入服务器IP" />
        </el-form-item>
        <el-form-item label="清理文件路径" prop="filePath">
          <el-input v-model="logCleanupForm.filePath" placeholder="请输入清理文件路径" />
        </el-form-item>
        <el-form-item label="清理文件名称" prop="fileName">
          <el-input v-model="logCleanupForm.fileName" placeholder="请输入清理文件名称" />
        </el-form-item>
        <el-form-item label="清理时间" prop="cleanupTime">
          <el-date-picker
              v-model="logCleanupForm.cleanupTime"
              type="datetime"
              placeholder="请选择清理时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="操作类型" prop="operationType">
          <el-select v-model="logCleanupForm.operationType" placeholder="请选择操作类型" style="width: 100%">
            <el-option label="手动清理" value="手动清理" />
            <el-option label="定时任务" value="定时任务" />
          </el-select>
        </el-form-item>
        <el-form-item label="清理原因" prop="cleanupReason">
          <el-select v-model="logCleanupForm.cleanupReason" placeholder="请选择清理原因" style="width: 100%">
            <el-option label="文件达到阈值" value="文件达到阈值" />
            <el-option label="手动清理" value="手动清理" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作结果" prop="operationResult">
          <el-select v-model="logCleanupForm.operationResult" placeholder="请选择操作结果" style="width: 100%">
            <el-option label="成功" value="成功" />
            <el-option label="失败" value="失败" />
          </el-select>
        </el-form-item>
        <el-form-item label="失败原因" prop="failureReason">
          <el-input v-model="logCleanupForm.failureReason" placeholder="请输入失败原因" />
        </el-form-item>
        <el-form-item label="清理前大小" prop="beforeSize">
          <el-input v-model="logCleanupForm.beforeSize" placeholder="请输入清理前大小" />
        </el-form-item>
        <el-form-item label="清理后大小" prop="afterSize">
          <el-input v-model="logCleanupForm.afterSize" placeholder="请输入清理后大小" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="logCleanupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleLogCleanupSubmit" :loading="logCleanupSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'

// 路由
const route = useRoute()
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}

// 激活的选项卡
const activeTab = ref('dependencies')

// 详情数据
const detailData = ref({
  id: 1,
  applicationName: '应用A',
  product: '产品A',
  status: 'enabled',
  type: 'web',
  organization: '机构A',
  projectManager: '张三',
  ssoSystemId: 'SSO001',
  gitUrl: 'https://github.com/example/appA',
  developmentLanguage: 'Java',
  avgPublishDuration: '4.8分钟',
  developmentTeam: '开发一组',
  applicationCategory: '业务系统',
  sourceCodeId: 'SC001',
  databaseId: 'DB001',
  publishCount: 25,
  avgStartupDuration: '2.1秒',
  testingTeam: '测试一组',
  applicationSubCategory: '订单系统',
  productionAppId: 'PA001',
  ossBucketId: 'OSS001',
  publishDuration: '120分钟'
})

// 依赖数据
const dependenciesData = ref([
  {
    groupId: 'org.springframework',
    artifactId: 'spring-core',
    version: '5.3.20'
  },
  {
    groupId: 'org.springframework',
    artifactId: 'spring-context',
    version: '5.3.20'
  },
  {
    groupId: 'org.springframework',
    artifactId: 'spring-web',
    version: '5.3.20'
  },
  {
    groupId: 'org.mybatis',
    artifactId: 'mybatis',
    version: '3.5.9'
  },
  {
    groupId: 'mysql',
    artifactId: 'mysql-connector-java',
    version: '8.0.28'
  }
])

// 应用日志数据
const appLogsData = ref([
  {
    serverIp: '*************',
    logPath: '/var/log/app1/',
    fileCount: 15,
    over2GCount: 3,
    maxStorage: '10GB',
    createTime: '2023-01-15 08:30:00',
    updateTime: '2023-06-20 14:45:00'
  },
  {
    serverIp: '*************',
    logPath: '/var/log/app2/',
    fileCount: 22,
    over2GCount: 1,
    maxStorage: '15GB',
    createTime: '2023-02-10 09:15:00',
    updateTime: '2023-06-21 16:20:00'
  },
  {
    serverIp: '*************',
    logPath: '/var/log/app3/',
    fileCount: 8,
    over2GCount: 0,
    maxStorage: '8GB',
    createTime: '2023-03-05 10:00:00',
    updateTime: '2023-06-19 11:30:00'
  }
])

// 日志清理记录数据
const logCleanupData = ref([
  {
    serverIp: '*************',
    filePath: '/var/log/app1/',
    fileName: 'app1.log',
    cleanupTime: '2023-06-15 08:30:00',
    operationType: '定时任务',
    cleanupReason: '文件达到阈值',
    operationResult: '成功',
    failureReason: '',
    beforeSize: '12GB',
    afterSize: '2GB'
  },
  {
    serverIp: '*************',
    filePath: '/var/log/app2/',
    fileName: 'app2.log',
    cleanupTime: '2023-06-16 09:15:00',
    operationType: '手动清理',
    cleanupReason: '手动清理',
    operationResult: '成功',
    failureReason: '',
    beforeSize: '8GB',
    afterSize: '1GB'
  },
  {
    serverIp: '*************',
    filePath: '/var/log/app3/',
    fileName: 'app3.log',
    cleanupTime: '2023-06-17 10:00:00',
    operationType: '定时任务',
    cleanupReason: '文件达到阈值',
    operationResult: '失败',
    failureReason: '权限不足',
    beforeSize: '15GB',
    afterSize: '15GB'
  }
])

// 日志清理记录对话框相关
const logCleanupDialogVisible = ref(false)
const logCleanupDialogTitle = ref('添加日志清理记录')
const logCleanupFormRef = ref<FormInstance>()
const logCleanupSubmitLoading = ref(false)
const logCleanupForm = reactive({
  serverIp: '',
  filePath: '',
  fileName: '',
  cleanupTime: '',
  operationType: '',
  cleanupReason: '',
  operationResult: '',
  failureReason: '',
  beforeSize: '',
  afterSize: ''
})

// 日志清理记录表单验证规则
const logCleanupRules: FormRules = {
  serverIp: [
    { required: true, message: '请输入服务器IP', trigger: 'blur' }
  ],
  filePath: [
    { required: true, message: '请输入清理文件路径', trigger: 'blur' }
  ],
  fileName: [
    { required: true, message: '请输入清理文件名称', trigger: 'blur' }
  ],
  cleanupTime: [
    { required: true, message: '请选择清理时间', trigger: 'change' }
  ],
  operationType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  cleanupReason: [
    { required: true, message: '请选择清理原因', trigger: 'change' }
  ],
  operationResult: [
    { required: true, message: '请选择操作结果', trigger: 'change' }
  ]
}

// 添加日志清理记录
const handleAddLogCleanup = () => {
  logCleanupDialogTitle.value = '添加日志清理记录'
  Object.assign(logCleanupForm, {
    serverIp: '',
    filePath: '',
    fileName: '',
    cleanupTime: '',
    operationType: '',
    cleanupReason: '',
    operationResult: '',
    failureReason: '',
    beforeSize: '',
    afterSize: ''
  })
  logCleanupDialogVisible.value = true
}

// 编辑日志清理记录
const handleEditLogCleanup = (row: any) => {
  logCleanupDialogTitle.value = '编辑日志清理记录'
  Object.assign(logCleanupForm, row)
  logCleanupDialogVisible.value = true
}

// 日志清理记录对话框关闭
const handleLogCleanupDialogClose = () => {
  logCleanupFormRef.value?.resetFields()
}

// 提交日志清理记录表单
const handleLogCleanupSubmit = async () => {
  if (!logCleanupFormRef.value) return

  try {
    await logCleanupFormRef.value.validate()
    logCleanupSubmitLoading.value = true

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 检查是添加还是编辑
    const isEdit = logCleanupForm.serverIp && logCleanupData.value.some(item => item.serverIp === logCleanupForm.serverIp && item.filePath === logCleanupForm.filePath);
    
    if (isEdit) {
      // 编辑操作
      const index = logCleanupData.value.findIndex(item => item.serverIp === logCleanupForm.serverIp && item.filePath === logCleanupForm.filePath);
      if (index !== -1) {
        logCleanupData.value[index] = { ...logCleanupForm };
      }
    } else {
      // 添加操作
      logCleanupData.value.push({ ...logCleanupForm });
    }

    ElMessage.success('操作成功')
    logCleanupDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    logCleanupSubmitLoading.value = false
  }
}

// 删除日志清理记录
const handleDeleteLogCleanup = (row: any) => {
  ElMessageBox.confirm('确定要删除这条日志清理记录吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 执行删除操作
    const index = logCleanupData.value.findIndex(item =>
      item.serverIp === row.serverIp &&
      item.filePath === row.filePath &&
      item.fileName === row.fileName
    );
    
    if (index !== -1) {
      logCleanupData.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    // 用户取消删除
    ElMessage.info('已取消删除');
  });
}

// 获取状态类型
const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    enabled: '启用',
    disabled: '下架',
    maintenance: '维护中'
  }
  return textMap[status] || status
}

onMounted(() => {
  // 获取应用ID
  const applicationId = route.params.id
  // 根据应用ID获取详情数据
  // 这里应该调用API获取实际数据
  console.log('应用ID:', applicationId)
})
</script>

<style scoped>
.application-detail-page {
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
}

.log-cleanup-header {
  margin-bottom: 15px;
  text-align: left;
}
</style>