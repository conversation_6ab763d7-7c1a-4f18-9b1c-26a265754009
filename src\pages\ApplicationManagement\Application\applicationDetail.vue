<template>
  <div class="application-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" size="default" plain>
          返回列表
        </el-button>
      </div>
      <div class="header-center">
        <h2 class="page-title">{{ detailData.appName || '应用详情' }}</h2>
      </div>
      <div class="header-right">
        <el-tag :type="getStatusType(detailData.status)" size="large">
          {{ getStatusText(detailData.status) }}
        </el-tag>
      </div>
    </div>

    <!-- 基本信息 -->
    <el-card class="detail-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><InfoFilled /></el-icon>
          <span class="header-title">基本信息</span>
        </div>
      </template>

      <div class="info-grid">
        <div class="info-section">
          <h4 class="section-title">应用信息</h4>
          <el-descriptions :column="1" size="default" border>
            <el-descriptions-item label="应用名称" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.appName || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="应用ID" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.appId || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="应用类型" label-class-name="desc-label">
              <el-tag size="small">{{ getTypeText(detailData.type) || '-' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="所属机构" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.organizationName || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="所属产品" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.productNames || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="info-section">
          <h4 class="section-title">团队信息</h4>
          <el-descriptions :column="1" size="default" border>
            <el-descriptions-item label="负责开发组" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.devTeam || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="负责测试组" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.testTeam || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目经理" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.projectManager || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="开发语言" label-class-name="desc-label">
              <el-tag size="small" type="info">{{ detailData.developLanguage || '-' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Git地址" label-class-name="desc-label">
              <el-link v-if="detailData.gitAddress" :href="detailData.gitAddress" target="_blank" type="primary">
                {{ detailData.gitAddress }}
              </el-link>
              <span v-else class="desc-value">-</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 性能统计 -->
    <el-card class="detail-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><TrendCharts /></el-icon>
          <span class="header-title">性能统计</span>
        </div>
      </template>

      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">
            <el-icon color="#409EFF"><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ detailData.moduleCount || 0 }}</div>
            <div class="stat-label">模块数量</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">
            <el-icon color="#67C23A"><Upload /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ detailData.totalPublishCount || 0 }}</div>
            <div class="stat-label">总发布次数</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">
            <el-icon color="#E6A23C"><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDuration(detailData.avgPublishDuration) }}</div>
            <div class="stat-label">平均发布时长</div>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">
            <el-icon color="#F56C6C"><VideoPlay /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDuration(detailData.avgStartupDuration) }}</div>
            <div class="stat-label">平均启动时长</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 系统标识 -->
    <el-card class="detail-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><Key /></el-icon>
          <span class="header-title">系统标识</span>
        </div>
      </template>

      <div class="info-grid">
        <div class="info-section">
          <h4 class="section-title">应用分类</h4>
          <el-descriptions :column="1" size="default" border>
            <el-descriptions-item label="应用大类" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.appMajorCategory || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="应用小类" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.appMinorCategory || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="info-section">
          <h4 class="section-title">系统标识</h4>
          <el-descriptions :column="1" size="default" border>
            <el-descriptions-item label="生产应用标识" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.prodAppIdentifier || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="SSO系统标识" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.ssoSystemFlag || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="源码工程标识" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.sourceProjectFlag || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="数据库标识" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.databaseFlag || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="OSS私有文件桶标识" label-class-name="desc-label">
              <span class="desc-value">{{ detailData.ossPrivateBucketFlag || '-' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import {
  ArrowLeft,
  InfoFilled,
  TrendCharts,
  DataAnalysis,
  Upload,
  Timer,
  VideoPlay,
  Key
} from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import ApplicationApi, { type PamsAppSummaryInfoDetailVO, type SysDictValueDO } from '@/api/Application'

// 路由
const route = useRoute()
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.back()
}

// 加载状态
const loading = ref(false)

// 详情数据
const detailData = ref<PamsAppSummaryInfoDetailVO>({
  id: 0,
  appId: 0,
  appName: '',
  status: '',
  type: '',
  organizationId: 0,
  moduleCount: 0,
  totalPublishCount: 0,
  totalPublishDuration: 0,
  avgPublishDuration: 0,
  avgStartupDuration: 0,
  devTeam: '',
  prodAppIdentifier: '',
  gitAddress: '',
  developLanguage: '',
  testTeam: '',
  projectManager: '',
  appMajorCategory: '',
  appMinorCategory: '',
  ssoSystemFlag: '',
  sourceProjectFlag: '',
  databaseFlag: '',
  ossPrivateBucketFlag: '',
  createTime: '',
  updateTime: '',
  creator: '',
  updater: '',
  deleted: false,
  organizationName: '',
  productNames: ''
})

// 字典选项
const statusOptions = ref<SysDictValueDO[]>([])
const typeOptions = ref<SysDictValueDO[]>([])

// 格式化时长（毫秒转换为可读格式）
const formatDuration = (milliseconds: number | null | undefined): string => {
  if (!milliseconds || milliseconds === 0) return '-'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 获取状态类型
const getStatusType = (status: string): 'success' | 'info' | 'warning' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'primary' | 'danger'> = {
    start: 'success',
    stop: 'info',
    maintain: 'warning',
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusOption = statusOptions.value.find(option => option.value === status)
  return statusOption ? statusOption.label : status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeOption = typeOptions.value.find(option => option.value === type)
  return typeOption ? typeOption.label : type
}

// 加载字典数据
const loadDictOptions = async () => {
  try {
    const [statusResponse, typeResponse] = await Promise.all([
      ApplicationApi.getDictValuesByKeyName('app_status'),
      ApplicationApi.getDictValuesByKeyName('app_type')
    ])

    if (statusResponse.success && statusResponse.data) {
      statusOptions.value = statusResponse.data
    }

    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载应用详情
const loadAppDetail = async () => {
  const appId = route.params.id as string
  if (!appId) {
    ElMessage.error('缺少应用ID参数')
    return
  }

  loading.value = true
  try {
    const response = await ApplicationApi.getAppDetail(Number(appId))
    if (response.success && response.data) {
      detailData.value = response.data
    } else {
      ElMessage.error('获取应用详情失败')
    }
  } catch (error) {
    console.error('获取应用详情失败:', error)
    ElMessage.error('获取应用详情失败')
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await loadDictOptions()
  await loadAppDetail()
})
</script>

<style scoped>
.application-detail-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 0 0 auto;
}

.header-center {
  flex: 1;
  text-align: center;
}

.header-right {
  flex: 0 0 auto;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

/* 卡片样式 */
.detail-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.header-title {
  color: #303133;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.info-section {
  background: #fafbfc;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

/* 描述列表样式 */
:deep(.desc-label) {
  font-weight: 600;
  color: #606266;
  background-color: #f8f9fa;
}

.desc-value {
  color: #303133;
  font-weight: 500;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-right: 16px;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-detail-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .header-center {
    text-align: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-value {
    font-size: 20px;
  }
}
</style>