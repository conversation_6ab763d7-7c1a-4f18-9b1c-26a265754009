<template>
  <div class="application-detail-component">
    <!-- 基本信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">基本信息</span>
        </div>
      </template>

      <!-- 基本信息表格：3列7行（实际6列：标签+内容） -->
      <el-table :data="basicInfoTableData" border style="width: 100%" class="basic-info-table" :show-header="false">
        <!-- 第一组：标签+内容 -->
        <el-table-column width="120" align="center">
          <template #default="{ row }">
            <div class="field-label">{{ row.field1.label }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row }">
            <span v-if="row.field1.label === '应用名称'">{{ row.field1.value }}</span>
            <el-link v-else-if="row.field1.label === 'git地址' && row.field1.value !== '-'" :href="row.field1.value" target="_blank" type="primary">
              {{ row.field1.value }}
            </el-link>
            <el-tag v-else-if="row.field1.label === '开发语言' && row.field1.value !== '-'" size="small" type="info">{{ row.field1.value }}</el-tag>
            <el-tag v-else-if="row.field1.label === '状态' && row.field1.value !== '-'" :type="getStatusType(detailData.status)">{{ row.field1.value }}</el-tag>
            <el-tag v-else-if="row.field1.label === '类型' && row.field1.value !== '-'" size="small">{{ row.field1.value }}</el-tag>
            <span v-else>{{ row.field1.value }}</span>
          </template>
        </el-table-column>

        <!-- 第二组：标签+内容 -->
        <el-table-column width="120" align="center">
          <template #default="{ row }">
            <div class="field-label">{{ row.field2.label }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row }">
            <span v-if="row.field2.label === '应用名称'" class="app-name-cell">{{ row.field2.value }}</span>
            <el-link v-else-if="row.field2.label === 'git地址及分支' && row.field2.value !== '-'" :href="row.field2.value" target="_blank" type="primary">
              {{ row.field2.value }}
            </el-link>
            <el-tag v-else-if="row.field2.label === '开发语言' && row.field2.value !== '-'" size="small" type="info">{{ row.field2.value }}</el-tag>
            <el-tag v-else-if="row.field2.label === '状态' && row.field2.value !== '-'" :type="getStatusType(detailData.status)">{{ row.field2.value }}</el-tag>
            <el-tag v-else-if="row.field2.label === '类型' && row.field2.value !== '-'" size="small">{{ row.field2.value }}</el-tag>
            <span v-else>{{ row.field2.value }}</span>
          </template>
        </el-table-column>

        <!-- 第三组：标签+内容 -->
        <el-table-column width="120" align="center">
          <template #default="{ row }">
            <div class="field-label">{{ row.field3.label }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template #default="{ row }">
            <span v-if="row.field3.label === '应用名称'" class="app-name-cell">{{ row.field3.value }}</span>
            <el-link v-else-if="row.field3.label === 'git地址及分支' && row.field3.value !== '-'" :href="row.field3.value" target="_blank" type="primary">
              {{ row.field3.value }}
            </el-link>
            <el-tag v-else-if="row.field3.label === '开发语言' && row.field3.value !== '-'" size="small" type="info">{{ row.field3.value }}</el-tag>
            <el-tag v-else-if="row.field3.label === '状态' && row.field3.value !== '-'" :type="getStatusType(detailData.status)">{{ row.field3.value }}</el-tag>
            <el-tag v-else-if="row.field3.label === '类型' && row.field3.value !== '-'" size="small">{{ row.field3.value }}</el-tag>
            <span v-else>{{ row.field3.value }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 更多信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">更多信息</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 依赖选项卡 -->
        <el-tab-pane label="依赖" name="dependencies">
          <el-table :data="dependenciesData" border style="width: 100%" class="center-table">
            <el-table-column prop="groupId" label="groupId" align="center" header-align="center" />
            <el-table-column prop="artifactId" label="artifactId" align="center" header-align="center" />
            <el-table-column prop="version" label="version" align="center" header-align="center" />
          </el-table>
        </el-tab-pane>

        <!-- 应用日志选项卡 -->
        <el-tab-pane label="应用日志" name="appLogs">
          <el-table :data="appLogsData" border style="width: 100%" class="center-table">
            <el-table-column prop="serverIp" label="服务器IP" align="center" header-align="center" />
            <el-table-column prop="logPath" label="日志地址" align="center" header-align="center" />
            <el-table-column prop="logFileCount" label="日志文件数量" align="center" header-align="center" />
            <el-table-column prop="largeFileCount" label="超过3G的文件数量" align="center" header-align="center" />
            <el-table-column prop="maxFileSize" label="最大文件存储量" align="center" header-align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" header-align="center" />
            <el-table-column prop="updateTime" label="更新时间" align="center" header-align="center" />
          </el-table>
        </el-tab-pane>

        <!-- 日志清理记录选项卡 -->
        <el-tab-pane label="日志清理记录" name="logCleanup">
          <div class="table-header">
            <el-button type="primary" @click="handleAddCleanupRecord">添加</el-button>
          </div>
          <el-table :data="logCleanupData" border style="width: 100%" class="center-table">
            <el-table-column prop="serverIp" label="服务器IP" align="center" header-align="center" />
            <el-table-column prop="cleanupPath" label="清理文件路径" align="center" header-align="center" />
            <el-table-column prop="cleanupFileName" label="清理文件名称" align="center" header-align="center" />
            <el-table-column prop="cleanupTime" label="清理时间" align="center" header-align="center" />
            <el-table-column prop="operationType" label="操作类型" align="center" header-align="center" />
            <el-table-column prop="cleanupReason" label="清理原因" align="center" header-align="center" />
            <el-table-column prop="operationResult" label="操作结果" align="center" header-align="center" />
            <el-table-column prop="failureReason" label="失败原因" align="center" header-align="center" />
            <el-table-column prop="sizeBefore" label="清理前大小" align="center" header-align="center" />
            <el-table-column prop="sizeAfter" label="清理后大小" align="center" header-align="center" />
            <el-table-column label="操作" width="80" align="center" header-align="center">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEditCleanupRecord(scope.row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ApplicationApi, { type PamsAppSummaryInfoDetailVO, type SysDictValueDO } from '@/api/Application'

// 组件属性
interface Props {
  appId: number | null
  visible: boolean
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 加载状态
const loading = ref(false)

// 当前选中的选项卡
const activeTab = ref('dependencies')

// 依赖数据接口
interface DependencyData {
  groupId: string
  artifactId: string
  version: string
}

// 应用日志数据接口
interface AppLogData {
  serverIp: string
  logPath: string
  logFileCount: number
  largeFileCount: number
  maxFileSize: string
  createTime: string
  updateTime: string
}

// 日志清理记录数据接口
interface LogCleanupData {
  serverIp: string
  cleanupPath: string
  cleanupFileName: string
  cleanupTime: string
  operationType: string
  cleanupReason: string
  operationResult: string
  failureReason: string
  sizeBefore: string
  sizeAfter: string
}

// 各种数据
const dependenciesData = ref<DependencyData[]>([])
const appLogsData = ref<AppLogData[]>([])
const logCleanupData = ref<LogCleanupData[]>([
  {
    serverIp: '',
    cleanupPath: '',
    cleanupFileName: '',
    cleanupTime: '',
    operationType: '手动清理/定时任务',
    cleanupReason: '文件达到阈值/手动清理',
    operationResult: '成功/失败',
    failureReason: '',
    sizeBefore: '',
    sizeAfter: ''
  }
])





// 详情数据
const detailData = ref<PamsAppSummaryInfoDetailVO>({
  id: 0,
  appId: 0,
  appName: '',
  status: '',
  type: '',
  organizationId: 0,
  moduleCount: 0,
  totalPublishCount: 0,
  totalPublishDuration: 0,
  avgPublishDuration: 0,
  avgStartupDuration: 0,
  devTeam: '',
  prodAppIdentifier: '',
  gitAddress: '',
  developLanguage: '',
  testTeam: '',
  projectManager: '',
  appMajorCategory: '',
  appMinorCategory: '',
  ssoSystemFlag: '',
  sourceProjectFlag: '',
  databaseFlag: '',
  ossPrivateBucketFlag: '',
  createTime: '',
  updateTime: '',
  creator: '',
  updater: '',
  deleted: false,
  organizationName: '',
  productNames: ''
})

// 字典选项
const statusOptions = ref<SysDictValueDO[]>([])
const typeOptions = ref<SysDictValueDO[]>([])

// 格式化时长（毫秒转换为可读格式）
const formatDuration = (milliseconds: number | null | undefined): string => {
  if (!milliseconds || milliseconds === 0) return '-'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 获取状态类型
const getStatusType = (status: string): 'success' | 'info' | 'warning' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'primary' | 'danger'> = {
    start: 'success',
    stop: 'info',
    maintain: 'warning',
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusOption = statusOptions.value.find(option => option.value === status)
  return statusOption ? statusOption.label : status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeOption = typeOptions.value.find(option => option.value === type)
  return typeOption ? typeOption.label : type
}

// 基本信息表格数据（3列7行）
const basicInfoTableData = computed(() => {
  const fields = [
    { label: '应用名称', value: detailData.value.appName || '-' },
    { label: '所属产品', value: detailData.value.productNames || '-' },
    { label: '所属组/公司', value: detailData.value.organizationName || '-' },
    { label: '负责开发组', value: detailData.value.devTeam || '-' },
    { label: '负责项目经理', value: detailData.value.projectManager || '-' },
    { label: '负责测试组', value: detailData.value.testTeam || '-' },
    { label: '开发语言', value: detailData.value.developLanguage || '-' },
    { label: '状态', value: getStatusText(detailData.value.status) },
    { label: 'git地址', value: detailData.value.gitAddress || '-' },
    { label: '应用大类', value: detailData.value.appMajorCategory || '-' },
    { label: '应用小类', value: detailData.value.appMinorCategory || '-' },
    { label: '平均发布时长', value: formatDuration(detailData.value.avgPublishDuration) },
    { label: '总发布时长', value: formatDuration(detailData.value.totalPublishDuration) },
    { label: '总发布次数', value: String(detailData.value.totalPublishCount || 0) },
    { label: '类型', value: getTypeText(detailData.value.type) },
    { label: '平均启动时长', value: formatDuration(detailData.value.avgStartupDuration) },
    { label: '生产应用标识', value: detailData.value.prodAppIdentifier || '-' },
    { label: '源码工程标识', value: detailData.value.sourceProjectFlag || '-' },
    { label: 'OSS私有文件桶标识', value: detailData.value.ossPrivateBucketFlag || '-' },
    { label: '数据库标识', value: detailData.value.databaseFlag || '-' },
    { label: 'SSO系统标识', value: detailData.value.ssoSystemFlag || '-' }
  ]

  const rows = []
  for (let i = 0; i < 7; i++) {
    rows.push({
      field1: fields[i] || { label: '', value: '-' },
      field2: fields[i + 7] || { label: '', value: '-' },
      field3: fields[i + 14] || { label: '', value: '-' }
    })
  }

  return rows
})



// 加载字典数据
const loadDictOptions = async () => {
  try {
    const [statusResponse, typeResponse] = await Promise.all([
      ApplicationApi.getDictValuesByKeyName('app_status'),
      ApplicationApi.getDictValuesByKeyName('app_type')
    ])

    if (statusResponse.success && statusResponse.data) {
      statusOptions.value = statusResponse.data
    }

    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载应用详情
const loadAppDetail = async () => {
  if (!props.appId) {
    return
  }

  loading.value = true
  try {
    const response = await ApplicationApi.getAppDetail(props.appId)
    if (response.success && response.data) {
      detailData.value = response.data
    } else {
      ElMessage.error('获取应用详情失败')
    }
  } catch (error) {
    console.error('获取应用详情失败:', error)
    ElMessage.error('获取应用详情失败')
  } finally {
    loading.value = false
  }
}

// 添加日志清理记录
const handleAddCleanupRecord = () => {
  console.log('添加日志清理记录')
  // TODO: 实现添加功能
}

// 编辑日志清理记录
const handleEditCleanupRecord = (row: LogCleanupData) => {
  console.log('编辑日志清理记录:', row)
  // TODO: 实现编辑功能
}

// 监听appId变化，重新加载数据
watch(() => props.appId, async (newAppId) => {
  if (newAppId && props.visible) {
    await loadDictOptions()
    await loadAppDetail()
  }
})

// 监听visible变化
watch(() => props.visible, async (newVisible) => {
  if (newVisible && props.appId) {
    await loadDictOptions()
    await loadAppDetail()
  }
})
</script>

<style scoped>
.application-detail-component {
  padding: 0;
}

/* 卡片样式 */
.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-title {
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.header-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #409EFF;
  border-radius: 2px;
}

/* 描述列表样式美化 */
:deep(.desc-label) {
  font-weight: 600 !important;
  color: #606266 !important;
  background-color: #f8f9fa !important;
  border-right: 2px solid #409EFF !important;
  width: 120px !important; /* 固定标签宽度 */
  text-align: center !important; /* 标签居中 */
}

:deep(.desc-content-center .el-descriptions__content) {
  text-align: center !important; /* 应用名称内容居中 */
}

.desc-value {
  color: #303133;
  font-weight: 500;
}

/* 选项卡样式 */
.detail-tabs {
  margin-top: 20px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}

/* 表格头部操作区域 */
.table-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* 表格居中样式 */
:deep(.center-table .el-table__header th) {
  text-align: center !important;
}

:deep(.center-table .el-table__body td) {
  text-align: center !important;
}

/* 基本信息表格样式 */
:deep(.basic-info-table .el-table__body td) {
  text-align: center !important;
  padding: 12px 8px !important;
}

:deep(.basic-info-table .el-table__body tr:hover > td) {
  background-color: #f0f9ff !important;
}

/* 标签列背景色（浅蓝色） */
:deep(.basic-info-table .el-table__body td:nth-child(1)),
:deep(.basic-info-table .el-table__body td:nth-child(3)),
:deep(.basic-info-table .el-table__body td:nth-child(5)) {
  background-color: #e6f3ff !important;
}

.field-label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.app-name-cell {
  font-weight: 600;
  color: #409EFF;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .application-detail-component {
    padding: 0;
  }

  :deep(.el-col) {
    margin-bottom: 16px;
  }
}
</style>