<template>
  <div class="application-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select v-model="searchForm.application" placeholder="应用" clearable>
            <el-option label="应用A" value="appA" />
            <el-option label="应用B" value="appB" />
            <el-option label="应用C" value="appC" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.product" placeholder="产品" clearable>
            <el-option label="产品A" value="productA" />
            <el-option label="产品B" value="productB" />
            <el-option label="产品C" value="productC" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.type" placeholder="类型" clearable>
            <el-option label="Web应用" value="web" />
            <el-option label="移动应用" value="mobile" />
            <el-option label="桌面应用" value="desktop" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.organization" placeholder="所属机构" clearable>
            <el-option label="机构A" value="orgA" />
            <el-option label="机构B" value="orgB" />
            <el-option label="机构C" value="orgC" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="applicationName" label="应用名称" min-width="100" />
        <el-table-column prop="product" label="所属产品" min-width="100" />
        <el-table-column prop="status" label="状态" width="100" sortable>
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="organization" label="所属机构" min-width="120" />
        <el-table-column prop="moduleCount" label="模块数量" width="100" sortable />
        <el-table-column prop="publishCount" label="总发布次数" width="160" sortable />
        <el-table-column prop="publishDuration" label="总发布时长" width="160" sortable />
        <el-table-column prop="avgPublishDuration" label="平均发布时长" width="160" sortable />
        <el-table-column prop="avgStartupDuration" label="平均启动时长" width="160" sortable />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="handleDialogClose"
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
      >
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入应用描述"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-input v-model="form.owner" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyId">
          <el-select v-model="form.companyId" placeholder="请选择所属公司" style="width: 100%">
            <el-option
                v-for="company in companies"
                :key="company.id"
                :label="company.name"
                :value="company.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择应用分类" style="width: 100%">
            <el-option label="内部系统" value="internal" />
            <el-option label="第三方系统" value="third-party" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择应用状态" style="width: 100%">
            <el-option label="启用" value="enabled" />
            <el-option label="下架" value="disabled" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-form-item>

      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 应用详情弹窗 -->
    <el-dialog
        v-model="detailVisible"
        title="应用详情"
        width="1200px"
        @close="handleDetailClose"
    >
      <div v-if="detailData" class="application-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="应用名称">{{ detailData.applicationName }}</el-descriptions-item>
                <el-descriptions-item label="所属产品">{{ detailData.product }}</el-descriptions-item>
                <el-descriptions-item label="所属机构">{{ detailData.organization }}</el-descriptions-item>
                <el-descriptions-item label="负责项目经理">{{ detailData.projectManager || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="SSO系统标识">{{ detailData.ssoSystemId || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="git地址及分支">{{ detailData.gitUrl || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="开发语言">{{ detailData.developmentLanguage || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="平均发布时长">{{ detailData.avgPublishDuration }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="8">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="getStatusType(detailData.status)">
                    {{ getStatusText(detailData.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="负责开发组">{{ detailData.developmentTeam || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="应用大类">{{ detailData.applicationCategory || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="源码工程标识">{{ detailData.sourceCodeId || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="数据库标识">{{ detailData.databaseId || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="总发布次数">{{ detailData.publishCount }}</el-descriptions-item>
                <el-descriptions-item label="平均启动时长">{{ detailData.avgStartupDuration }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="8">
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
                <el-descriptions-item label="类型">{{ detailData.type }}</el-descriptions-item>
                <el-descriptions-item label="负责测试组">{{ detailData.testingTeam || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="应用小类">{{ detailData.applicationSubCategory || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="生产应用标识">{{ detailData.productionAppId || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="OSS私有文件桶标识">{{ detailData.ossBucketId || '暂无' }}</el-descriptions-item>
                <el-descriptions-item label="总发布时长">{{ detailData.publishDuration }}</el-descriptions-item>
                <el-descriptions-item label="&nbsp;">&nbsp;</el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>

        <!-- 更多信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>更多信息</span>
            </div>
          </template>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="依赖" name="dependencies">
              <el-table :data="dependenciesData" style="width: 100%" max-height="400">
                <el-table-column prop="groupId" label="groupId" width="200" />
                <el-table-column prop="artifactId" label="artifactId" width="200" />
                <el-table-column prop="version" label="version" width="150" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="应用日志" name="appLogs">
              <el-table :data="appLogsData" style="width: 100%" max-height="400">
                <el-table-column prop="serverIp" label="服务器IP" width="120" />
                <el-table-column prop="logPath" label="日志地址" width="200" />
                <el-table-column prop="fileCount" label="日志文件数量" width="120" />
                <el-table-column prop="over2GCount" label="超过2G的文件数量" width="150" />
                <el-table-column prop="maxStorage" label="最大文件存储量" width="120" />
                <el-table-column prop="createTime" label="创建时间" width="150" />
                <el-table-column prop="updateTime" label="更新时间" width="150" />
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="日志清理记录" name="logCleanup">
              <div class="log-cleanup-header">
                <el-button type="primary" size="small" @click="handleAddLogCleanup">
                  添加
                </el-button>
              </div>
              <el-table :data="logCleanupData" style="width: 100%" max-height="400">
                <el-table-column prop="serverIp" label="服务器IP" width="120" />
                <el-table-column prop="filePath" label="清理文件路径" width="150" />
                <el-table-column prop="fileName" label="清理文件名称" width="120" />
                <el-table-column prop="cleanupTime" label="清理时间" width="150" />
                <el-table-column prop="operationType" label="操作类型" width="100" />
                <el-table-column prop="cleanupReason" label="清理原因" width="120" />
                <el-table-column prop="operationResult" label="操作结果" width="80" />
                <el-table-column prop="failureReason" label="失败原因" width="100" />
                <el-table-column prop="beforeSize" label="清理前大小" width="100" />
                <el-table-column prop="afterSize" label="清理后大小" width="100" />
                <el-table-column label="操作" width="80" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="handleEditLogCleanup(row)">
                      编辑
    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 日志清理记录编辑对话框 -->
        <el-dialog
            v-model="logCleanupDialogVisible"
            :title="logCleanupDialogTitle"
            width="600px"
            @close="handleLogCleanupDialogClose"
        >
          <el-form
              ref="logCleanupFormRef"
              :model="logCleanupForm"
              :rules="logCleanupRules"
              label-width="120px"
          >
            <el-form-item label="服务器IP" prop="serverIp">
              <el-input v-model="logCleanupForm.serverIp" placeholder="请输入服务器IP" />
            </el-form-item>
            <el-form-item label="清理文件路径" prop="filePath">
              <el-input v-model="logCleanupForm.filePath" placeholder="请输入清理文件路径" />
            </el-form-item>
            <el-form-item label="清理文件名称" prop="fileName">
              <el-input v-model="logCleanupForm.fileName" placeholder="请输入清理文件名称" />
            </el-form-item>
            <el-form-item label="清理时间" prop="cleanupTime">
              <el-date-picker
                  v-model="logCleanupForm.cleanupTime"
                  type="datetime"
                  placeholder="请选择清理时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="操作类型" prop="operationType">
              <el-select v-model="logCleanupForm.operationType" placeholder="请选择操作类型" style="width: 100%">
                <el-option label="手动清理" value="手动清理" />
                <el-option label="定时任务" value="定时任务" />
              </el-select>
            </el-form-item>
            <el-form-item label="清理原因" prop="cleanupReason">
              <el-select v-model="logCleanupForm.cleanupReason" placeholder="请选择清理原因" style="width: 100%">
                <el-option label="文件达到阈值" value="文件达到阈值" />
                <el-option label="手动清理" value="手动清理" />
              </el-select>
            </el-form-item>
            <el-form-item label="操作结果" prop="operationResult">
              <el-select v-model="logCleanupForm.operationResult" placeholder="请选择操作结果" style="width: 100%">
                <el-option label="成功" value="成功" />
                <el-option label="失败" value="失败" />
              </el-select>
            </el-form-item>
            <el-form-item label="失败原因" prop="failureReason">
              <el-input v-model="logCleanupForm.failureReason" placeholder="请输入失败原因" />
            </el-form-item>
            <el-form-item label="清理前大小" prop="beforeSize">
              <el-input v-model="logCleanupForm.beforeSize" placeholder="请输入清理前大小" />
            </el-form-item>
            <el-form-item label="清理后大小" prop="afterSize">
              <el-input v-model="logCleanupForm.afterSize" placeholder="请输入清理后大小" />
            </el-form-item>
          </el-form>
          <template #footer>
        <span class="dialog-footer">
          <el-button @click="logCleanupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleLogCleanupSubmit" :loading="logCleanupSubmitLoading">
            确定
          </el-button>
        </span>
          </template>
        </el-dialog>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEditFromDetail" v-if="detailData">
            编辑应用
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Refresh, Plus, Link, Document } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  application: '',
  product: '',
  status: '',
  type: '',
  organization: ''
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    applicationName: '应用A',
    product: '产品A',
    status: 'enabled',
    type: 'web',
    organization: '机构A',
    moduleCount: 12,
    publishCount: 25,
    publishDuration: '120分钟',
    avgPublishDuration: '4.8分钟',
    avgStartupDuration: '2.1秒'
  },
  {
    id: 2,
    applicationName: '应用B',
    product: '产品B',
    status: 'disabled',
    type: 'mobile',
    organization: '机构B',
    moduleCount: 8,
    publishCount: 16,
    publishDuration: '85分钟',
    avgPublishDuration: '5.3分钟',
    avgStartupDuration: '1.8秒'
  },
  {
    id: 3,
    applicationName: '应用C',
    product: '产品A',
    status: 'maintenance',
    type: 'desktop',
    organization: '机构C',
    moduleCount: 15,
    publishCount: 32,
    publishDuration: '180分钟',
    avgPublishDuration: '5.6分钟',
    avgStartupDuration: '3.2秒'
  },
  {
    id: 4,
    applicationName: '应用D',
    product: '产品C',
    status: 'enabled',
    type: 'web',
    organization: '机构A',
    moduleCount: 6,
    publishCount: 9,
    publishDuration: '45分钟',
    avgPublishDuration: '5.0分钟',
    avgStartupDuration: '1.5秒'
  },
  {
    id: 5,
    applicationName: '应用E',
    product: '产品B',
    status: 'enabled',
    type: 'mobile',
    organization: '机构B',
    moduleCount: 10,
    publishCount: 18,
    publishDuration: '95分钟',
    avgPublishDuration: '5.3分钟',
    avgStartupDuration: '2.0秒'
  }
])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 5
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('新增应用')
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

// 详情弹窗
const detailVisible = ref(false)
const detailData = ref<any | null>(null)
const activeTab = ref('dependencies')
const dependenciesData = ref([
  {
    groupId: 'org.springframework',
    artifactId: 'spring-core',
    version: '5.3.20'
  },
  {
    groupId: 'org.springframework',
    artifactId: 'spring-context',
    version: '5.3.20'
  },
  {
    groupId: 'org.springframework',
    artifactId: 'spring-web',
    version: '5.3.20'
  },
  {
    groupId: 'org.mybatis',
    artifactId: 'mybatis',
    version: '3.5.9'
  },
  {
    groupId: 'mysql',
    artifactId: 'mysql-connector-java',
    version: '8.0.28'
  }
])
const logCleanupData = ref([
  {
    serverIp: '*************',
    filePath: '/var/log/app1/',
    fileName: 'app1.log',
    cleanupTime: '2023-06-15 08:30:00',
    operationType: '定时任务',
    cleanupReason: '文件达到阈值',
    operationResult: '成功',
    failureReason: '',
    beforeSize: '12GB',
    afterSize: '2GB'
  },
  {
    serverIp: '*************',
    filePath: '/var/log/app2/',
    fileName: 'app2.log',
    cleanupTime: '2023-06-16 09:15:00',
    operationType: '手动清理',
    cleanupReason: '手动清理',
    operationResult: '成功',
    failureReason: '',
    beforeSize: '8GB',
    afterSize: '1GB'
  },
  {
    serverIp: '*************',
    filePath: '/var/log/app3/',
    fileName: 'app3.log',
    cleanupTime: '2023-06-17 10:00:00',
    operationType: '定时任务',
    cleanupReason: '文件达到阈值',
    operationResult: '失败',
    failureReason: '权限不足',
    beforeSize: '15GB',
    afterSize: '15GB'
  }
])

// 日志清理记录对话框相关
const logCleanupDialogVisible = ref(false)
const logCleanupDialogTitle = ref('添加日志清理记录')
const logCleanupFormRef = ref<FormInstance>()
const logCleanupSubmitLoading = ref(false)
const logCleanupForm = reactive({
  serverIp: '',
  filePath: '',
  fileName: '',
  cleanupTime: '',
  operationType: '',
  cleanupReason: '',
  operationResult: '',
  failureReason: '',
  beforeSize: '',
  afterSize: ''
})

// 日志清理记录表单验证规则
const logCleanupRules: FormRules = {
  serverIp: [
    { required: true, message: '请输入服务器IP', trigger: 'blur' }
  ],
  filePath: [
    { required: true, message: '请输入清理文件路径', trigger: 'blur' }
  ],
  fileName: [
    { required: true, message: '请输入清理文件名称', trigger: 'blur' }
  ],
  cleanupTime: [
    { required: true, message: '请选择清理时间', trigger: 'change' }
  ],
  operationType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  cleanupReason: [
    { required: true, message: '请选择清理原因', trigger: 'change' }
  ],
  operationResult: [
    { required: true, message: '请选择操作结果', trigger: 'change' }
  ]
}

// 添加日志清理记录
const handleAddLogCleanup = () => {
  logCleanupDialogTitle.value = '添加日志清理记录'
  Object.assign(logCleanupForm, {
    serverIp: '',
    filePath: '',
    fileName: '',
    cleanupTime: '',
    operationType: '',
    cleanupReason: '',
    operationResult: '',
    failureReason: '',
    beforeSize: '',
    afterSize: ''
  })
  logCleanupDialogVisible.value = true
}

// 编辑日志清理记录
const handleEditLogCleanup = (row: any) => {
  logCleanupDialogTitle.value = '编辑日志清理记录'
  Object.assign(logCleanupForm, row)
  logCleanupDialogVisible.value = true
}

// 日志清理记录对话框关闭
const handleLogCleanupDialogClose = () => {
  logCleanupFormRef.value?.resetFields()
}

// 提交日志清理记录表单
const handleLogCleanupSubmit = async () => {
  if (!logCleanupFormRef.value) return

  try {
    await logCleanupFormRef.value.validate()
    logCleanupSubmitLoading.value = true

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里应该根据实际需求添加或更新数据
    ElMessage.success('操作成功')
    logCleanupDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    logCleanupSubmitLoading.value = false
  }
}

const appLogsData = ref([
  {
    serverIp: '*************',
    logPath: '/var/log/app1/',
    fileCount: 15,
    over2GCount: 3,
    maxStorage: '10GB',
    createTime: '2023-01-15 08:30:00',
    updateTime: '2023-06-20 14:45:00'
  },
  {
    serverIp: '*************',
    logPath: '/var/log/app2/',
    fileCount: 22,
    over2GCount: 1,
    maxStorage: '15GB',
    createTime: '2023-02-10 09:15:00',
    updateTime: '2023-06-21 16:20:00'
  },
  {
    serverIp: '*************',
    logPath: '/var/log/app3/',
    fileCount: 8,
    over2GCount: 0,
    maxStorage: '8GB',
    createTime: '2023-03-05 10:00:00',
    updateTime: '2023-06-19 11:30:00'
  }
])

// 表单数据
const form = reactive({
  id: '',
  name: '',
  description: '',
  owner: '',
  companyId: '',
  category: '',
  status: 'enabled',
  environments: [
    {
      environment: 'production',
      domain: '',
      description: '生产环境',
      isDefault: true,
      swaggerUrl: ''
    }
  ],
  swaggerConfigs: [] as any[]
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' }
  ],
  owner: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  companyId: [
    { required: true, message: '请选择所属公司', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择应用分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择应用状态', trigger: 'change' }
  ],
  environments: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要配置一个环境域名'))
          return
        }

        for (const env of value ) {
          if (!env.domain) {
            callback(new Error('请填写所有环境的域名'))
            return
          }
          if (!/^https?:\/\/.+/.test(env.domain)) {
            callback(new Error('请输入有效的域名，如：https://api.example.com'))
            return
          }

        }

        const defaultCount = value.filter((env: any) => env.isDefault).length
        if (defaultCount === 0) {
          callback(new Error('请设置一个默认环境'))
          return
        }
        if (defaultCount > 1) {
          callback(new Error('只能设置一个默认环境'))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  swaggerConfigs: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value || value.length === 0) {
          callback() // swagger配置是可选的
          return
        }

        for (const swagger of value) {
          if (swagger.name && !swagger.url) {
            callback(new Error('请填写Swagger配置的URL'))
            return
          }
        }

        callback()
      },
      trigger: 'blur'
    }
  ]
}

// 获取公司列表
const companies = reactive([])

// 获取状态类型
const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    enabled: '启用',
    disabled: '下架',
    maintenance: '维护中'
  }
  return textMap[status] || status
}

// 获取环境文本
const getEnvironmentText = (environment: string): string => {
  const textMap: Record<string, string> = {
    development: '开发环境',
    testing: '测试环境',
    staging: '预发环境',
    production: '生产环境'
  }
  return textMap[environment] || environment
}

// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    application: '',
    product: '',
    status: '',
    type: '',
    organization: ''
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增应用'
  Object.assign(form, {
    id: '',
    name: '',
    description: '',
    owner: '',
    companyId: '',
    category: '',
    status: 'enabled',
    environments: [
      {
        environment: 'production',
        domain: '',
        description: '生产环境',
        isDefault: true,
        swaggerUrl: ''
      }
    ],
    swaggerConfigs: []
  })
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑应用'
  Object.assign(form, row)
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  detailData.value = row
  detailVisible.value = true
}

// 详情弹窗关闭
const handleDetailClose = () => {
  detailData.value = null
}

// 从详情弹窗编辑
const handleEditFromDetail = () => {
  if (detailData.value) {
    handleEdit(detailData.value)
    detailVisible.value = false
  }
}

// 获取模块数量
const getModuleCount = (applicationId: string): number => {
  return 0;
}

// 获取接口数量
const getInterfaceCount = (applicationId: string): number => {
  return 0;
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除应用"${row.name}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    // 模拟删除

  } catch {
    // 用户取消删除
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (form.id) {
      // 编辑

      ElMessage.success('编辑成功')
    } else {
      // 新增

      pagination.total++
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 添加环境配置
const addEnvironment = () => {
  form.environments.push({
    environment: 'development',
    domain: '',
    description: '',
    isDefault: false,
    swaggerUrl: ''
  })
}

// 删除环境配置
const removeEnvironment = (index: number) => {
  form.environments.splice(index, 1)
}

// 添加Swagger配置
const addSwaggerConfig = () => {
  form.swaggerConfigs.push({
    id: Date.now().toString(),
    name: '',
    url: '',
    description: '',
    environment: undefined,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  })
}

// 删除Swagger配置
const removeSwaggerConfig = (index: number) => {
  form.swaggerConfigs.splice(index, 1)
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  form.swaggerConfigs = []
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    // 使用静态数据
    let filteredData: any[] = [
      {
        id: 1,
        applicationName: '应用A',
        product: '产品A',
        status: 'enabled',
        type: 'web',
        organization: '机构A',
        moduleCount: 12,
        publishCount: 25,
        publishDuration: '120分钟',
        avgPublishDuration: '4.8分钟',
        avgStartupDuration: '2.1秒'
      },
      {
        id: 2,
        applicationName: '应用B',
        product: '产品B',
        status: 'disabled',
        type: 'mobile',
        organization: '机构B',
        moduleCount: 8,
        publishCount: 16,
        publishDuration: '85分钟',
        avgPublishDuration: '5.3分钟',
        avgStartupDuration: '1.8秒'
      },
      {
        id: 3,
        applicationName: '应用C',
        product: '产品A',
        status: 'maintenance',
        type: 'desktop',
        organization: '机构C',
        moduleCount: 15,
        publishCount: 32,
        publishDuration: '180分钟',
        avgPublishDuration: '5.6分钟',
        avgStartupDuration: '3.2秒'
      },
      {
        id: 4,
        applicationName: '应用D',
        product: '产品C',
        status: 'enabled',
        type: 'web',
        organization: '机构A',
        moduleCount: 6,
        publishCount: 9,
        publishDuration: '45分钟',
        avgPublishDuration: '5.0分钟',
        avgStartupDuration: '1.5秒'
      },
      {
        id: 5,
        applicationName: '应用E',
        product: '产品B',
        status: 'enabled',
        type: 'mobile',
        organization: '机构B',
        moduleCount: 10,
        publishCount: 18,
        publishDuration: '95分钟',
        avgPublishDuration: '5.3分钟',
        avgStartupDuration: '2.0秒'
      }
    ]

    // 应用筛选
    if (searchForm.application) {
      filteredData = filteredData.filter(item => item.applicationName.includes(searchForm.application))
    }

    // 产品筛选
    if (searchForm.product) {
      filteredData = filteredData.filter(item => item.product.includes(searchForm.product))
    }

    // 状态筛选
    if (searchForm.status) {
      filteredData = filteredData.filter(item => item.status === searchForm.status)
    }

    // 类型筛选
    if (searchForm.type) {
      filteredData = filteredData.filter(item => item.type === searchForm.type)
    }

    // 所属机构筛选
    if (searchForm.organization) {
      filteredData = filteredData.filter(item => item.organization.includes(searchForm.organization))
    }

    // 分页
    const start = (pagination.page - 1) * pagination.pageSize
    const end = start + pagination.pageSize

    pagination.total = filteredData.length
    // 使用slice方法实现分页
    const pageData = filteredData.slice(start, end)
    // 更新表格数据
    tableData.value = pageData

    loading.value = false
  }, 500)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
</style>
