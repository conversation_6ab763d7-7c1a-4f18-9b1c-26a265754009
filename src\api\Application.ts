import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

// 应用信息汇总表查询参数接口
export interface PamsAppSummaryInfoQueryParams {
  // 分页参数
  page?: number
  pageSize?: number
  
  // 查询条件
  id?: number
  appId?: number
  appName?: string
  status?: string // start=启用/stop=下架/maintain=维护中
  type?: string // app=APP/frontend=前端/backend=后端
  organizationId?: number
  organizationName?: string
  devTeam?: string
  prodAppIdentifier?: string
  developLanguage?: string
  testTeam?: string
  projectManager?: string
  appMajorCategory?: string
  appMinorCategory?: string
  ssoSystemFlag?: string
  sourceProjectFlag?: string
  databaseFlag?: string
  ossPrivateBucketFlag?: string
  productName?: string
}

// 应用信息汇总表返回数据接口
export interface PamsAppSummaryInfoPageVO {
  id: number
  appId: number
  appName: string
  status: string
  type: string
  organizationId: number
  moduleCount: number
  totalPublishCount: number
  totalPublishDuration: number
  avgPublishDuration: number
  avgStartupDuration: number
  devTeam: string
  prodAppIdentifier: string
  gitAddress: string
  developLanguage: string
  testTeam: string
  projectManager: string
  appMajorCategory: string
  appMinorCategory: string
  ssoSystemFlag: string
  sourceProjectFlag: string
  databaseFlag: string
  ossPrivateBucketFlag: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  organizationName: string
  productNames: string
}

// 分页返回数据接口
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 下拉框选项接口
export interface DropdownOptionVO {
  label: string
  value: number
}

// API响应接口
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  success: boolean
}

export default {
  // 分页查询应用信息汇总表
  getPageInfo: function (params: PamsAppSummaryInfoQueryParams): Promise<ApiResponse<PageResult<PamsAppSummaryInfoPageVO>>> {
    return http.get(`${sysApi}/pamsAppSummaryInfo/page`, params)
  },

  // 获取应用下拉框选项
  getAppDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/pamsAppSummaryInfo/list`, {})
  },

  // 获取产品下拉框选项
  getProductDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/pamsBusinessSystemInfo/list`, {})
  },

  // 获取机构下拉框选项
  getOrganizationDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/organization/list`, {})
  }
}
