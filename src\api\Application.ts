import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

// 应用信息汇总表查询参数接口
export interface PamsAppSummaryInfoQueryParams {
  // 分页参数
  pageNum?: number
  pageSize?: number
  orderFields?: string
  orderRules?: string

  // 查询条件
  id?: number
  appId?: number
  appName?: string
  status?: string // start=启用/stop=下架/maintain=维护中
  type?: string // app=APP/frontend=前端/backend=后端
  organizationId?: number
  organizationName?: string
  devTeam?: string
  prodAppIdentifier?: string
  developLanguage?: string
  testTeam?: string
  projectManager?: string
  appMajorCategory?: string
  appMinorCategory?: string
  ssoSystemFlag?: string
  sourceProjectFlag?: string
  databaseFlag?: string
  ossPrivateBucketFlag?: string
  productName?: string
}

// 应用信息汇总表返回数据接口
export interface PamsAppSummaryInfoPageVO {
  id: number
  appId: number
  appName: string
  status: string
  type: string
  organizationId: number
  moduleCount: number
  totalPublishCount: number
  totalPublishDuration: number
  avgPublishDuration: number
  avgStartupDuration: number
  devTeam: string
  prodAppIdentifier: string
  gitAddress: string
  developLanguage: string
  testTeam: string
  projectManager: string
  appMajorCategory: string
  appMinorCategory: string
  ssoSystemFlag: string
  sourceProjectFlag: string
  databaseFlag: string
  ossPrivateBucketFlag: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  organizationName: string
  productNames: string
}

// 分页返回数据接口
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 下拉框选项接口
export interface DropdownOptionVO {
  label: string
  value: number
}

// 字典值接口
export interface SysDictValueDO {
  id: number
  keyId: number
  keyName: string
  value: string
  label: string
  description: string
  sort: number
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
}

// 应用详情接口
export interface PamsAppSummaryInfoDetailVO {
  id: number
  appId: number
  appName: string
  status: string
  type: string
  organizationId: number
  moduleCount: number
  totalPublishCount: number
  totalPublishDuration: number
  avgPublishDuration: number
  avgStartupDuration: number
  devTeam: string
  prodAppIdentifier: string
  gitAddress: string
  developLanguage: string
  testTeam: string
  projectManager: string
  appMajorCategory: string
  appMinorCategory: string
  ssoSystemFlag: string
  sourceProjectFlag: string
  databaseFlag: string
  ossPrivateBucketFlag: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
  organizationName: string
  productNames: string
}

// API响应接口
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  success: boolean
}

export default {
  // 分页查询应用信息汇总表
  getPageInfo: function (params: PamsAppSummaryInfoQueryParams): Promise<ApiResponse<PageResult<PamsAppSummaryInfoPageVO>>> {
    return http.get(`${sysApi}/pamsAppSummaryInfo/page`, params)
  },

  // 获取应用下拉框选项
  getAppDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/pamsAppSummaryInfo/list`, {})
  },

  // 获取产品下拉框选项
  getProductDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/pamsBusinessSystemInfo/list`, {})
  },

  // 获取机构下拉框选项
  getOrganizationDropdownOptions: function (): Promise<ApiResponse<DropdownOptionVO[]>> {
    return http.get(`${sysApi}/organization/list`, {})
  },

  // 根据keyName查询字典配置值列表
  getDictValuesByKeyName: function (keyName: string): Promise<ApiResponse<SysDictValueDO[]>> {
    return http.get(`${sysApi}/sysDictValue/list/${keyName}`, {})
  },

  // 根据id查询应用信息汇总表详情
  getAppDetail: function (id: number): Promise<ApiResponse<PamsAppSummaryInfoDetailVO>> {
    return http.get(`${sysApi}/pamsAppSummaryInfo/info/${id}`, {})
  }
}
